import { PageContainer, ProLayout } from '@ant-design/pro-components'
import React, { useState, useEffect } from 'react'
import { getMainNavRoutes, getSubNavRoutes, getCurrentRoutes, findRouteByPath } from '@/router'
import Logo from '@/assets/icons/logo.png'
import AuthorAvatar from '@/assets/images/author.jpg'
import { styled } from 'styled-components'
import { useNavigate, useLocation, Routes, Route } from 'react-router-dom'
import { LanguageProvider } from '@/locales/LanguageContext'
import { createStyles } from 'antd-style'
import { AppBuilder } from './pages/App/AppBuilder'
import { AuthProvider } from './contexts/AuthContext'

const useStyles = createStyles(() => ({
  layout: {
    '.ant-pro-sider-logo': {
      borderBlockEnd: '0px solid rgba(0, 0, 0, 0.06)',
    },
    '.ant-pro-sider-logo > a > img': {
      height: '33px !important',
    },
    '.ant-pro-layout .ant-pro-sider-logo >a': {
      minWidth: '33px !important',
    },
    '.ant-pro-base-menu-vertical.ant-pro-base-menu-vertical-collapsed .ant-menu-item': {
      paddingInline: '0 !important',
      marginBlock: '4px !important',
      marginBottom: '10px !important',
    },
    '.ant-pro-layout .ant-pro-sider .ant-layout-sider-children': {
      backgroundColor: '#fff !important',
    },
  },
}))

// 子导航组件
const SubNavProLayout = styled(ProLayout)`
  /* 精准匹配折叠状态的 Sider */

  .ant-pro-sider.ant-layout-sider-collapsed {
    width: 52px !important;
    flex: 0 0 52px !important;
    max-width: 52px !important;
    min-width: 52px !important;
  }

  .ant-menu-item-selected {
    background-color: rgb(239 246 255 / 1);
    color: rgb(37 99 235 / 1);
    font-weight: 500;
  }

  .ant-layout-sider-collapsed .ant-layout-sider-children {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    padding-inline: 8px;
    padding-block: 0;
    border-inline-end: 1px solid rgba(5, 5, 5, 0.06) !important;
    margin-inline-end: -1px;
  }
`

// 页面容器
const StyledPageContainer = styled(PageContainer)`
  .ant-pro-page-container-warp-page-header {
    padding-block-start: 10px;
    padding-inline-start: 20px;
  }
`

const renderRoutes = routes => {
  return routes.map(route => {
    if (route.routes && route.routes.length > 0) {
      return (
        <Route key={route.path} path={route.path} element={route.element}>
          {renderRoutes(route.routes)}
        </Route>
      )
    }
    return <Route key={route.path} path={route.path} element={route.element} />
  })
}

const AppContent: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { styles } = useStyles()

  const [pathname, setPathname] = useState<string>(location.pathname)
  const [activeMainRoute, setActiveMainRoute] = useState<Route>()
  const [subNavRoutes, setSubNavRoutes] = useState<Route[]>([])
  const [currentRoutes, setCurrentRoutes] = useState<Route[]>([])

  // 监听路由变化，根据当前访问的路径更新导航菜单状态
  useEffect(() => {
    // 获取主导航菜单配置
    const mainNavMenus = getMainNavRoutes()
    // 查找当前路径匹配的主导航项
    const currentMainRoute = mainNavMenus.find(nav => location.pathname.startsWith(nav.path))
    if (currentMainRoute) {
      // 设置根导航名称
      setActiveMainRoute(currentMainRoute)
      // 获取并设置子导航菜单
      setSubNavRoutes(getSubNavRoutes(currentMainRoute.path))
      // 获取并设置当前路由配置
      setCurrentRoutes(getCurrentRoutes(currentMainRoute.path))
      // 更新当前路径
      setPathname(location.pathname)
    }
  }, [location.pathname])

  const currentRoute = findRouteByPath(location.pathname)

  if (currentRoute?.isStandalone) {
    return (
      <Routes>
        <Route key="/app/builder/:id" path="/app/builder/:id" element={<AppBuilder />} />
      </Routes>
    )
  }

  return (
    <div
      id="pro-layout"
      className={styles.layout}
      style={{
        height: '100vh',
      }}
    >
      <ProLayout
        logo={Logo}
        title="Blueprint.AI"
        collapsed={true}
        token={{
          sider: {
            // colorBgCollapsedButton: string;
            // colorTextCollapsedButtonHover: string;
            // colorTextCollapsedButton: string;
            // colorMenuBackground: string;
            // menuHeight: number;
            // colorBgMenuItemCollapsedElevated: string;
            // colorMenuItemDivider: string;
            // colorBgMenuItemHover: string;
            // colorBgMenuItemActive: 'rgb(37 99 235 / 1)',
            colorBgMenuItemSelected: 'rgb(239 246 255 / 1)',
            colorTextMenuSelected: 'rgb(37 99 235 / 1)',
            // colorTextMenuItemHover: string;
            // colorTextMenuActive: string;
            // colorTextMenu: string;
            // colorTextMenuSecondary: string;
            // paddingInlineLayoutMenu: number;
            // paddingBlockLayoutMenu: number;
            // /**
            //  * menu 顶部 title 的字体颜色
            //  */
            // colorTextMenuTitle: string;
            // colorTextSubMenuSelected: string;
          },
          pageContainer: {
            paddingBlockPageContainerContent: 0,
            paddingInlinePageContainerContent: 0,
            colorBgPageContainer: '#fff',
            colorBgPageContainerFixed: '#fff',
          },
        }}
        collapsedButtonRender={false}
        layout="side"
        route={{
          routes: getMainNavRoutes(),
        }}
        location={{
          pathname,
        }}
        menu={{
          type: 'group',
          collapsedShowTitle: true,
        }}
        avatarProps={{
          src: AuthorAvatar,
          title: 'Alex',
          size: 'small',
        }}
        actionsRender={false}
        menuFooterRender={false}
        onMenuHeaderClick={e => console.log(e)}
        menuItemRender={(item, dom) => (
          <a
            onClick={() => {
              let path = item.path || '/assistant'
              setActiveMainRoute(item)

              const subNavRoutes = getSubNavRoutes(path)
              if (subNavRoutes.length > 0) {
                path = subNavRoutes[0].path
                setCurrentRoutes(subNavRoutes)
                setSubNavRoutes(subNavRoutes)
              } else {
                path = item.path
                setCurrentRoutes([item])
                setSubNavRoutes([])
              }
              setPathname(path)
              navigate(path)
            }}
          >
            {dom}
          </a>
        )}
      >
        <SubNavProLayout
          className="overflow-hidden"
          location={{
            pathname: pathname,
          }}
          fixSiderbar={false}
          route={{
            routes: subNavRoutes.filter(route => !route.hidden),
          }}
          siderWidth={150}
          style={{
            height: '100vh',
          }}
          suppressSiderWhenMenuEmpty={true}
          menu={{
            hideMenuWhenCollapsed: true,
          }}
          menuHeaderRender={props => {
            return (
              <div className="text-lg font-medium text-gray-800">{activeMainRoute?.fullName}</div>
            )
          }}
          menuItemRender={(item, dom) => (
            <a
              onClick={() => {
                const path = item.path || '/assistant'
                setPathname(path)
                navigate(path)
              }}
            >
              {dom}
            </a>
          )}
        >
          <StyledPageContainer
            className="overflow-y-auto"
            header={{
              breadcrumb: {},
            }}
            token={{
              paddingBlockPageContainerContent: 0,
              paddingInlinePageContainerContent: 0,
            }}
          >
            {/* {findRouteElement(currentRoutes, location.pathname)} */}
            <Routes>{renderRoutes(currentRoutes)}</Routes>
          </StyledPageContainer>
        </SubNavProLayout>
      </ProLayout>
    </div>
  )
}

const App: React.FC = () => {
  return (
    <LanguageProvider>
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </LanguageProvider>
  )
}

export default App
