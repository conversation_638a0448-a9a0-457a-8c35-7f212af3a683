import axios, { AxiosResponse, AxiosError, InternalAxiosRequestConfig } from 'axios'
import { config } from '../config/env'

// 自定义错误类型
interface CustomError extends Error {
  status?: number
  data?: any
}

// 创建一个事件总线用于跨组件通信
export const authEvents = {
  // 触发401未授权事件
  emit401Error: () => {
    console.log('触发401未授权事件')
    const event = new CustomEvent('auth:unauthorized')
    window.dispatchEvent(event)
  },
  // 监听401未授权事件
  on401Error: (callback: () => void) => {
    console.log('添加401未授权事件监听')
    window.addEventListener('auth:unauthorized', callback)
  },
  // 移除401未授权事件监听
  off401Error: (callback: () => void) => {
    window.removeEventListener('auth:unauthorized', callback)
  },
}

// 创建 axios 实例
const request = axios.create({
  baseURL: config.API_BASE_URL,
  timeout: 60000,
})

request.interceptors.request.use((url, options) => {
  // 从localStorage获取token
  const token = localStorage.getItem('token')

  return {
    url: `${config.API_BASE_URL}${url}`,
    options: {
      ...options,
      timeout: 60000,
      headers: {
        // 如果有token则使用，否则不设置Authorization头
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
        // 如果URL包含 /files/upload ，则不设置 Content-Type 为 application/json
        ...(url.includes('/files/upload') ? {} : { 'Content-Type': 'application/json' }),
        ...(options.headers || {}),
      },
    },
  }
})

// 处理响应拦截
request.interceptors.response.use(
  async response => {
    try {
      const contentType = response.headers.get('Content-Type')
      console.log('响应状态码:', response.status)

      // 如果是401未授权错误，直接触发全局事件并拒绝Promise
      if (response.status === 401) {
        console.log('检测到401未授权错误，直接处理')
        authEvents.emit401Error()
        const error = new Error('未授权，请登录') as CustomError
        error.status = 401
        return Promise.reject(error)
      }

      // 如果是 SSE 响应，直接返回原始 response
      if (contentType && contentType.includes('text/event-stream')) {
        return response
      }

      // 如果是 Blob 类型的响应（如图片）
      if (
        contentType &&
        (contentType.includes('image') || contentType.includes('application/octet-stream'))
      ) {
        return response.blob()
      }

      // 如果是文本响应
      if (contentType && contentType.includes('text/')) {
        return response.text()
      }

      // 处理 JSON 响应
      const data = await response.clone().json()

      // 处理业务错误
      if (!response.ok) {
        const error = new Error(data.message || response.statusText || '服务器错误') as CustomError
        error.status = response.status
        error.data = data

        // 再次检查是否是401未授权错误（以防前面的检查没有捕获到）
        if (response.status === 401) {
          console.log('在JSON处理中检测到401未授权错误')
          authEvents.emit401Error()
        }

        return Promise.reject(error)
      }

      return data
    } catch (error) {
      // 处理解析错误
      if (error instanceof SyntaxError) {
        return Promise.reject(new Error('响应数据格式错误'))
      }
      // 处理其他错误
      return Promise.reject(error)
    }
  },
  // 添加错误处理函数来捕获网络错误
  error => {
    console.log('请求发生错误:', error)
    // 检查是否是401错误
    if (error.response && error.response.status === 401) {
      console.log('在错误处理中检测到401未授权错误')
      authEvents.emit401Error()
    }
    return Promise.reject(error)
  }
)

export default request
