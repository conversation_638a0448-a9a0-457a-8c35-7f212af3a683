import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useCallback,
  useEffect,
} from 'react'
import LoginModal from '@/components/LoginModal'
import { authEvents } from '@/utils/request'

interface AuthContextType {
  showLoginModal: () => void
  hideLoginModal: () => void
  isLoginModalVisible: boolean
  checkTokenExists: () => boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [loginModalVisible, setLoginModalVisible] = useState(false)

  const showLoginModal = useCallback(() => {
    setLoginModalVisible(true)
  }, [])

  const hideLoginModal = useCallback(() => {
    setLoginModalVisible(false)
  }, [])

  const handleLoginSuccess = useCallback(
    (token: string) => {
      // 保存token到localStorage (已在LoginModal组件中处理)
      // 隐藏登录模态框
      hideLoginModal()
      // 刷新当前页面以应用新的token
      window.location.reload()
    },
    [hideLoginModal]
  )

  // 检查token是否存在的函数
  const checkTokenExists = useCallback(() => {
    const token = localStorage.getItem('token')
    if (!token) {
      console.log('未找到token，显示登录模态框')
      showLoginModal()
      return false
    }
    return true
  }, [showLoginModal])

  // 应用启动时检查token
  useEffect(() => {
    console.log('应用启动，检查token状态')
    checkTokenExists()
  }, [checkTokenExists])

  // 监听401未授权事件
  useEffect(() => {
    // 当接收到401未授权错误时，显示登录模态框
    const handle401Error = () => {
      console.log('接收到401未授权事件，显示登录模态框')
      showLoginModal()
    }

    console.log('设置401未授权事件监听')
    // 添加事件监听
    authEvents.on401Error(handle401Error)

    // 组件卸载时移除事件监听
    return () => {
      console.log('移除401未授权事件监听')
      authEvents.off401Error(handle401Error)
    }
  }, [showLoginModal])

  return (
    <AuthContext.Provider
      value={{
        showLoginModal,
        hideLoginModal,
        isLoginModalVisible: loginModalVisible,
        checkTokenExists,
      }}
    >
      {children}
      <LoginModal
        visible={loginModalVisible}
        onCancel={hideLoginModal}
        onSuccess={handleLoginSuccess}
      />
    </AuthContext.Provider>
  )
}
