import React, { useState } from 'react'
import { Modal, Form, Input, Button, QRCode, Flex, message, Checkbox } from 'antd'
import { loginApiV1AuthLoginPost } from '@/api/auth'
import { UserOutlined, LockOutlined } from '@ant-design/icons'
import Logo from '@/assets/icons/logo.png'

interface LoginModalProps {
  visible: boolean
  onCancel: () => void
  onSuccess: (token: string) => void
}

const LoginModal: React.FC<LoginModalProps> = ({ visible, onCancel, onSuccess }) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [agreeTerms, setAgreeTerms] = useState(false)

  // 处理账号密码登录
  const handleLogin = async () => {
    try {
      const values = await form.validateFields()

      if (!agreeTerms) {
        message.error('请阅读并同意用户协议和隐私政策')
        return
      }

      setLoading(true)

      // 调用登录API
      const result = await loginApiV1AuthLoginPost({
        username: values.username,
        password: values.password,
      })

      if (result && result.access_token) {
        // 保存token到localStorage
        localStorage.setItem('token', result.access_token)
        message.success('登录成功')
        onSuccess(result.access_token)
      }
    } catch (error) {
      console.error('登录失败:', error)
      message.error('登录失败，请检查用户名和密码')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Modal
      title={null}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={550}
      centered
      closable={false}
      className="rounded-xl overflow-hidden"
    >
      <div className="flex gap-5 pt-5 pb-8">
        <div className="flex flex-col justify-center items-center">
          {/* <span className="text-base font-medium h-5 mb-3">关注微信公众号</span> */}
          <div className="flex-1 flex flex-col justify-center items-center">
            <QRCode
              value="http://localhost:5173"
              icon={Logo}
              size={150}
              bordered={false}
              keyboard={false}
              className="m-5"
            />
            <span className="text-sm h-5 mb-3">关注微信公众号</span>
          </div>
        </div>
        <div className="flex flex-1 flex-col justify-center ">
          <span className="text-base font-medium h-5">账号密码登录</span>
          <Form form={form} layout="vertical" onFinish={handleLogin} className="flex-1 mt-5 w-full">
            <Form.Item name="username" rules={[{ required: true, message: '请输入用户名' }]}>
              <Input
                prefix={<UserOutlined className="text-gray-400" />}
                placeholder="请输入用户名"
              />
            </Form.Item>
            <Form.Item name="password" rules={[{ required: true, message: '请输入密码' }]}>
              <Input.Password
                prefix={<LockOutlined className="text-gray-400" />}
                placeholder="请输入密码"
              />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading} block>
                登录
              </Button>
            </Form.Item>
            <div className="flex items-center">
              <Checkbox
                checked={agreeTerms}
                onChange={e => setAgreeTerms(e.target.checked)}
                className="text-xs text-gray-400"
              >
                <span className="text-xs text-gray-400">
                  我已阅读并同意{' '}
                  <a href="#" className="text-blue-500">
                    《用户协议》
                  </a>{' '}
                  和{' '}
                  <a href="#" className="text-blue-500">
                    《隐私政策》
                  </a>
                </span>
              </Checkbox>
            </div>
          </Form>
        </div>
      </div>
    </Modal>
  )
}

export default LoginModal
